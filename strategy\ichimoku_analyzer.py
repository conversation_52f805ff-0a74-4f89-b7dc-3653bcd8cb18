# ichimoku_bitget_bot/strategy/ichimoku_analyzer.py

import logging
import pandas as pd # Using pandas for easier data manipulation and Ichimoku calculation
# You might need to install pandas: pip install pandas
# Also, a library like 'pandas_ta' or implementing calculations manually is needed.
# Let's assume we will implement calculations manually for now or use a simple helper.

logger = logging.getLogger(__name__)

class IchimokuAnalyzer:
    def __init__(self, ichimoku_settings: dict):
        """
        Initializes the IchimokuAnalyzer.

        Args:
            ichimoku_settings: A dictionary containing Ichimoku periods.
                               Example: {'tenkan_period': 9, 'kijun_period': 26, 'senkou_span_b_period': 52, 'chikou_span_period': 26}
        """
        self.tenkan_period = ichimoku_settings.get('tenkan_period', 9)
        self.kijun_period = ichimoku_settings.get('kijun_period', 26)
        self.senkou_span_b_period = ichimoku_settings.get('senkou_span_b_period', 52)
        self.chikou_span_period = ichimoku_settings.get('chikou_span_period', 26) # Usually same as Kijun

        # Validate settings (basic check)
        if not all([self.tenkan_period > 0, self.kijun_period > 0, self.senkou_span_b_period > 0, self.chikou_span_period >= 0]):
            logger.error("Invalid Ichimoku settings: Periods must be positive integers.")
            # In a real bot, you might raise an error or use default values

    def analyze(self, ohlcv_data_by_timeframe: dict) -> dict:
        """
        Analyzes OHLCV data for Ichimoku components across multiple timeframes.

        Args:
            ohlcv_data_by_timeframe: A dictionary where keys are internal timeframe names
                                     and values are lists of OHLCV data.

        Returns:
            A dictionary where keys are timeframe names and values are dictionaries
            containing Ichimoku analysis results for that timeframe, or an empty dict
            if analysis fails.
            Example:
            {
                '1h': {
                    'latest_candle': [...], # The latest OHLCV candle
                    'ichimoku': {
                        'tenkan_sen': [...], # List of Tenkan-sen values
                        'kijun_sen': [...],  # List of Kijun-sen values
                        'senkou_span_a': [...], # List of Senkou Span A values
                        'senkou_span_b': [...], # List of Senkou Span B values
                        'chikou_span': [...], # List of Chikou Span values
                    },
                    'current_ichimoku_values': { # Values for the latest candle
                        'tenkan_sen': float,
                        'kijun_sen': float,
                        'senkou_span_a': float,
                        'senkou_span_b': float,
                        'chikou_span': float,
                    },
                    'cloud_state': 'green' or 'red', # State of the future cloud
                    'price_relation_to_cloud': 'above' or 'below' or 'within',
                    'price_relation_to_kijun': 'above' or 'below',
                    'tenkan_relation_to_kijun': 'above' or 'below',
                    # Add other relevant states/relations
                },
                '4h': { ... },
                '1d': { ... }
            }
        """
        analysis_results = {}

        for tf_name, ohlcv_list in ohlcv_data_by_timeframe.items():
            if not ohlcv_list or len(ohlcv_list) < max(self.tenkan_period, self.kijun_period, self.senkou_span_b_period, self.chikou_span_period) + self.chikou_span_period:
                 logger.warning(f"Not enough data for Ichimoku analysis on timeframe {tf_name}. Need at least {max(self.tenkan_period, self.kijun_period, self.senkou_span_b_period, self.chikou_span_period) + self.chikou_span_period} candles.")
                 continue

            try:
                # Convert list of lists to pandas DataFrame for easier calculation
                # Columns: timestamp, open, high, low, close, volume
                df = pd.DataFrame(ohlcv_list, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms') # Convert timestamp to datetime
                # Set timestamp as index if needed for some libraries, but not strictly necessary for manual calc

                # --- Ichimoku Calculations ---
                # Tenkan-sen (Conversion Line): (Highest High + Lowest Low) / 2 over the last tenkan_period periods
                df['tenkan_sen'] = (df['high'].rolling(window=self.tenkan_period).max() + df['low'].rolling(window=self.tenkan_period).min()) / 2

                # Kijun-sen (Base Line): (Highest High + Lowest Low) / 2 over the last kijun_period periods
                df['kijun_sen'] = (df['high'].rolling(window=self.kijun_period).max() + df['low'].rolling(window=self.kijun_period).min()) / 2

                # Senkou Span A (Leading Span A): (Tenkan-sen + Kijun-sen) / 2 plotted kijun_period periods ahead
                # Calculate the value first, then shift
                df['senkou_span_a_raw'] = (df['tenkan_sen'] + df['kijun_sen']) / 2
                df['senkou_span_a'] = df['senkou_span_a_raw'].shift(self.kijun_period) # Shift forward

                # Senkou Span B (Leading Span B): (Highest High + Lowest Low) / 2 over the last senkou_span_b_period periods, plotted kijun_period periods ahead
                # Calculate the value first, then shift
                df['senkou_span_b_raw'] = (df['high'].rolling(window=self.senkou_span_b_period).max() + df['low'].rolling(window=self.senkou_span_b_period).min()) / 2
                df['senkou_span_b'] = df['senkou_span_b_raw'].shift(self.kijun_period) # Shift forward

                # Chikou Span (Lagging Span): Closing price plotted chikou_period periods behind
                df['chikou_span'] = df['close'].shift(-self.chikou_span_period) # Shift backward

                # --- Analysis of Current State (for the latest candle) ---
                latest_candle_data = df.iloc[-1] # Get the last row (latest candle)

                # Cloud State (Future Cloud)
                # The cloud is formed by Senkou Span A and Senkou Span B.
                # The 'future' cloud is the part plotted ahead (shifted data).
                # We look at the values of Senkou Span A and B for the *current* time index,
                # but these values represent the cloud projected *forward* by kijun_period.
                # So, we check the relationship between the *shifted* Senkou Span A and B values
                # at the *current* index.
                current_ssa = latest_candle_data['senkou_span_a']
                current_ssb = latest_candle_data['senkou_span_b']

                cloud_state = None
                if pd.notna(current_ssa) and pd.notna(current_ssb):
                    cloud_state = 'green' if current_ssa > current_ssb else 'red'

                # Price Relation to Cloud (Current Price vs. Current Cloud)
                # The 'current' cloud for price relation is the cloud at the current time index,
                # which is formed by the *unshifted* Senkou Span A and B values.
                # However, standard Ichimoku analysis often refers to the price relative to the
                # cloud that is *currently* visible, which is the cloud projected forward.
                # Let's analyze the price relative to the *shifted* cloud boundaries at the current index.
                current_price = latest_candle_data['close']
                cloud_top = max(current_ssa, current_ssb) if pd.notna(current_ssa) and pd.notna(current_ssb) else None
                cloud_bottom = min(current_ssa, current_ssb) if pd.notna(current_ssa) and pd.notna(current_ssb) else None

                price_relation_to_cloud = None
                if cloud_top is not None and cloud_bottom is not None:
                    if current_price > cloud_top:
                        price_relation_to_cloud = 'above'
                    elif current_price < cloud_bottom:
                        price_relation_to_cloud = 'below'
                    else:
                        price_relation_to_cloud = 'within'

                # Price Relation to Kijun-sen (Current Price vs. Current Kijun)
                current_kijun = latest_candle_data['kijun_sen']
                price_relation_to_kijun = None
                if pd.notna(current_kijun):
                    price_relation_to_kijun = 'above' if current_price > current_kijun else 'below'

                # Tenkan-sen Relation to Kijun-sen (Current Tenkan vs. Current Kijun)
                current_tenkan = latest_candle_data['tenkan_sen']
                tenkan_relation_to_kijun = None
                if pd.notna(current_tenkan) and pd.notna(current_kijun):
                    tenkan_relation_to_kijun = 'above' if current_tenkan > current_kijun else 'below'

                # Chikou Span Relation to Price (Current Chikou vs. Price chikou_period periods ago)
                # Chikou Span value at current index is the closing price chikou_period periods ago.
                # We compare this Chikou Span value to the *current* price.
                current_chikou_value = latest_candle_data['chikou_span'] # This is the close price from chikou_period ago
                # We need the current price to compare it against.
                # The latest_candle_data['close'] is the current price.
                # So, we compare latest_candle_data['close'] (current price)
                # with the close price from chikou_period periods ago (which is stored in the chikou_span column at the *current* index).
                # This seems counter-intuitive, but the Chikou Span line itself is the shifted close price.
                # The *analysis* is comparing the Chikou Span line's *current position* (which is the past close)
                # to the *current* price.
                # Let's re-read the definition: Chikou Span is the current closing price plotted chikou_period periods *behind*.
                # So, the value in the 'chikou_span' column at index `i` is the closing price at index `i - self.chikou_period`.
                # To analyze the Chikou Span relative to the current price, we need the Chikou Span value
                # that corresponds to the *current* candle's close, but plotted in the past.
                # This means we need the 'chikou_span' value from an index `i` such that `i - self.chikou_period` is the index of the latest candle.
                # So, we need the 'chikou_span' value at index `latest_index + self.chikou_period`.
                # This requires looking into the future of the DataFrame, which we don't have.
                # A more practical approach for real-time analysis is to compare the *current* closing price
                # to the Kijun-sen and the Cloud *at the current time*.
                # The Chikou Span analysis is typically comparing the Chikou Span line's position
                # relative to the price chart *at the Chikou Span's plotted time*.
                # Let's simplify for now and focus on the other components.
                # If we need Chikou Span analysis, we'd compare the latest closing price
                # to the price chart (candles, Kijun, Cloud) shifted back by chikou_period.
                # This requires more complex indexing. Let's skip detailed Chikou analysis for now
                # unless specifically needed for a signal condition.

                # Store results for this timeframe
                analysis_results[tf_name] = {
                    'latest_candle': ohlcv_list[-1], # Keep the original list format for consistency if needed elsewhere
                    'ichimoku': {
                        'tenkan_sen': df['tenkan_sen'].tolist(),
                        'kijun_sen': df['kijun_sen'].tolist(),
                        'senkou_span_a': df['senkou_span_a'].tolist(),
                        'senkou_span_b': df['senkou_span_b'].tolist(),
                        'chikou_span': df['chikou_span'].tolist(),
                    },
                     'current_ichimoku_values': { # Values for the latest candle (last row of df)
                        'tenkan_sen': latest_candle_data['tenkan_sen'],
                        'kijun_sen': latest_candle_data['kijun_sen'],
                        'senkou_span_a': latest_candle_data['senkou_span_a'],
                        'senkou_span_b': latest_candle_data['senkou_span_b'],
                        'chikou_span': latest_candle_data['chikou_span'], # This is the close price from chikou_period ago
                        'close_price': latest_candle_data['close'] # Add current close price for clarity
                    },
                    'cloud_state': cloud_state,
                    'price_relation_to_cloud': price_relation_to_cloud,
                    'price_relation_to_kijun': price_relation_to_kijun,
                    'tenkan_relation_to_kijun': tenkan_relation_to_kijun,
                    # Add other relevant states/relations here
                }

            except Exception as e:
                logger.error(f"Error analyzing Ichimoku for timeframe {tf_name}: {e}", exc_info=True)
                # Continue to the next timeframe even if one fails

        if not analysis_results:
            logger.warning("Ichimoku analysis failed for all timeframes.")

        return analysis_results

    # Helper function to calculate rolling highest high/lowest low (alternative to pandas rolling)
    # def _rolling_min_max(self, data, window, func):
    #     """Helper to calculate rolling min or max."""
    #     result = []
    #     for i in range(len(data)):
    #         if i < window - 1:
    #             result.append(None) # Not enough data yet
    #         else:
    #             window_data = data[i - window + 1 : i + 1]
    #             result.append(func(window_data))
    #     return result

# Example usage (for testing purposes)
# def main():
#     # Dummy OHLCV data: [timestamp, open, high, low, close, volume]
#     # Need enough data points, e.g., > 52 + 26 = 78 candles for standard settings
#     dummy_ohlcv_1h = []
#     import time
#     current_time_ms = int(time.time() * 1000)
#     base_price = 10000
#     for i in range(150): # Generate 150 dummy candles
#         timestamp = current_time_ms - (150 - 1 - i) * 60000 # Simulate 1-minute candles
#         open_price = base_price + i * 0.1 + (i % 10) * 0.5 # Simple price trend
#         high_price = open_price + 2
#         low_price = open_price - 2
#         close_price = open_price + (i % 5 - 2) * 0.8 # Simulate some price movement
#         volume = 100 + i * 5
#         dummy_ohlcv_1h.append([timestamp, open_price, high_price, low_price, close_price, volume])

#     dummy_ohlcv_4h = [] # Add dummy data for other timeframes if needed
#     dummy_ohlcv_1d = []

#     ohlcv_data = {
#         '1h': dummy_ohlcv_1h,
#         # '4h': dummy_ohlcv_4h,
#         # '1d': dummy_ohlcv_1d,
#     }

#     ichimoku_settings = {
#         'tenkan_period': 9,
#         'kijun_period': 26,
#         'senkou_span_b_period': 52,
#         'chikou_span_period': 26
#     }

#     analyzer = IchimokuAnalyzer(ichimoku_settings)
#     analysis_results = analyzer.analyze(ohlcv_data)

#     if analysis_results:
#         print("Ichimoku Analysis Results:")
#         for tf, result in analysis_results.items():
#             print(f"\nTimeframe: {tf}")
#             latest_values = result['current_ichimoku_values']
#             print(f"  Latest Close: {latest_values['close_price']:.2f}")
#             print(f"  Latest Tenkan-sen: {latest_values['tenkan_sen']:.2f}")
#             print(f"  Latest Kijun-sen: {latest_values['kijun_sen']:.2f}")
#             print(f"  Latest Senkou Span A (shifted): {latest_values['senkou_span_a']:.2f}")
#             print(f"  Latest Senkou Span B (shifted): {latest_values['senkou_span_b']:.2f}")
#             # Chikou Span value is the close price from 26 periods ago
#             print(f"  Chikou Span Value (Close {ichimoku_settings['chikou_span_period']} periods ago): {latest_values['chikou_span']:.2f}")
#             print(f"  Cloud State (Future): {result['cloud_state']}")
#             print(f"  Price Relation to Cloud: {result['price_relation_to_cloud']}")
#             print(f"  Price Relation to Kijun: {result['price_relation_to_kijun']}")
#             print(f"  Tenkan Relation to Kijun: {result['tenkan_relation_to_kijun']}")

# if __name__ == "__main__":
#     main()
