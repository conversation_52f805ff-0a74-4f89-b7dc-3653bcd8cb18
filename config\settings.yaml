# API Settings (Replace with your actual API key and secret)
# IMPORTANT: For production, consider using environment variables or a secrets management system
api:
  key: "bg_a8374fc9a37065aedad2e1258defe9c7"
  secret: "a711884d35e432f5bfa90c98af82c3b5c29c6f2b5cde9eb6800047a6190978e5"
  password: "01097743296" # Required for Bitget

# Trading Symbols (List of symbols to trade on Bitget Spot against USDT)
# Example: BTCUSDT, ETHUSDT, XRPUSDT
trading_symbols:
  - "BTC/USDT"
  - "ETH/USDT"
  - "XRP/USDT"
  # Add more symbols as needed

# Timeframes to fetch data for (Used by OHLCVFetcher and IchimokuAnalyzer)
# Ensure these timeframes are supported by Bitget API
timeframes:
  '1d': '1d'
  '4h': '4h'
  '1h': '1h'
  # Add or modify timeframes as needed (e.g., '15m', '30m')

# Ichimoku Settings (Standard settings)
ichimoku:
  tenkan_period: 9
  kijun_period: 26
  senkou_span_b_period: 52
  chikou_span_period: 26 # Usually same as Kijun

# Strategy Settings (Used by SignalGenerator)
strategy:
  # Define conditions for BUY/SELL signals based on Ichimoku components
  # These are examples and should match the logic in signal_generator.py
  buy_conditions:
    # Example: Price above Kijun, Tenkan above Kijun, Price above Cloud, Future Cloud Green
    - "price_above_kijun"
    - "tenkan_above_kijun"
    - "price_above_cloud"
    - "future_cloud_green"
    # Add or modify conditions as needed

  sell_conditions:
    # Example: Price below Kijun, Tenkan below Kijun, Price below Cloud, Future Cloud Red
    - "price_below_kijun"
    - "tenkan_below_kijun"
    - "price_below_cloud"
    - "future_cloud_red"
    # Add or modify conditions as needed

  # Settings for 'Fresh' signals (e.g., 1h Fresh Complete)
  fresh_signal_lookback: 1 # Number of previous candles to check for signal absence

# Risk Management Settings (Used by RiskManager and potentially OrderManager)
risk_management:
  position_sizing_type: "fixed_percentage" # Options: "fixed_percentage", "fixed_amount", "risk_based"
  fixed_percentage: 0.01 # Percentage of available balance to use per trade (e.g., 0.01 for 1%)

  # Stop Loss Settings
  stop_loss_type: "fixed_percentage" # Options: "none", "fixed_percentage", "kijun_sen", "atr_trailing"
  fixed_stop_loss_percentage: 0.02 # Percentage below entry price (e.g., 0.02 for 2%)

  # Take Profit Settings
  take_profit_type: "none" # Options: "none", "fixed_percentage", "multiple_levels"
  fixed_take_profit_percentage: 0.03 # Percentage above entry price (e.g., 0.03 for 3%)
  # multiple_levels: # Example for multiple levels
  #   - percentage: 0.02
  #     ratio: 0.5 # Close 50% of position
  #   - percentage: 0.04
  #     ratio: 0.5 # Close remaining 50%

# Bot Operation Settings
scan_interval_seconds: 60 # How often the bot scans for signals (in seconds)
paper_trading: True # Set to True for paper trading, False for live trading

# Logging Settings (Basic settings, can be expanded in utils/logger.py)
logging:
  level: INFO # Options: DEBUG, INFO, WARNING, ERROR, CRITICAL
  file: bot.log # Log file name (optional)

# Database Settings (Optional, for storing trade history, etc.)
database:
  enabled: False
  type: "sqlite" # Options: "sqlite"
  path: "data/trade_history.db" # Path to the database file
