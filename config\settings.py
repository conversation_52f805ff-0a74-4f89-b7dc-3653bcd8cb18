# ichimoku_bitget_bot/config/settings.py

import yaml
import os
import logging

logger = logging.getLogger(__name__)

def load_settings(settings_path='config/settings.yaml', api_keys_path='config/api_keys.yaml'):
    """
    Loads settings from settings.yaml and API keys from api_keys.yaml.
    """
    settings = {}
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) # Go up two directories from current file

    full_settings_path = os.path.join(base_dir, settings_path)
    full_api_keys_path = os.path.join(base_dir, api_keys_path)

    # Load main settings
    if not os.path.exists(full_settings_path):
        logger.error(f"Settings file not found: {full_settings_path}")
        # Create a dummy settings file with basic structure
        dummy_settings = {
            'trading_pairs': ['BTC/USDT', 'ETH/USDT'],
            'bot_cycle_interval': 60, # seconds
            'strategy': {
                'main_timeframe': '1h',
                'ichimoku': {
                    'tenkan_period': 20,
                    'kijun_period': 60,
                    'senkou_period': 120,
                    'chikou_period': 30
                },
                # Add other strategy parameters here
            },
            'risk_management': {
                'position_size_percentage': 1.0, # 1% of balance
                'stop_loss_percentage': 2.0, # 2% below entry
                'take_profit_percentage': 4.0 # 4% above entry
                # Add other risk management parameters here
            },
            # Add other general bot settings here
        }
        try:
            with open(full_settings_path, 'w') as f:
                yaml.dump(dummy_settings, f, indent=4)
            logger.info(f"Created a dummy settings file: {full_settings_path}")
            settings.update(dummy_settings)
        except Exception as e:
            logger.error(f"Failed to create dummy settings file: {e}")
            return None
    else:
        try:
            with open(full_settings_path, 'r') as f:
                settings.update(yaml.safe_load(f))
        except yaml.YAMLError as e:
            logger.error(f"Error loading settings file: {e}")
            return None

    # Load API keys
    api_keys = {}
    if not os.path.exists(full_api_keys_path):
        logger.warning(f"API keys file not found: {full_api_keys_path}. Create this file with your keys.")
        # Create a dummy api_keys file
        dummy_api_keys = {
            'bitget': {
                'apiKey': 'YOUR_BITGET_API_KEY',
                'secret': 'YOUR_BITGET_SECRET_KEY',
                'password': 'YOUR_BITGET_PASSWORD' # Or passphrase
            }
        }
        try:
            with open(full_api_keys_path, 'w') as f:
                yaml.dump(dummy_api_keys, f, indent=4)
            logger.info(f"Created a dummy API keys file: {full_api_keys_path}")
            api_keys.update(dummy_api_keys)
        except Exception as e:
            logger.error(f"Failed to create dummy API keys file: {e}")
            # Continue without API keys, but trading won't work
    else:
        try:
            with open(full_api_keys_path, 'r') as f:
                api_keys.update(yaml.safe_load(f))
        except yaml.YAMLError as e:
            logger.error(f"Error loading API keys file: {e}")
            # Continue without API keys, but trading won't work

    settings['api_keys'] = api_keys

    return settings

# Example usage (for testing)
if __name__ == "__main__":
    loaded_settings = load_settings()
    if loaded_settings:
        print("Settings loaded successfully:")
        import json
        print(json.dumps(loaded_settings, indent=4))
