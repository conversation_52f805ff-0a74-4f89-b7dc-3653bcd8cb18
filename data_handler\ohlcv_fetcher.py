# ichimoku_bitget_bot/data_handler/ohlcv_fetcher.py

import logging
import asyncio
from ichimoku_bitget_bot.exchange_api.bitget_client import BitgetClient # Import the client

logger = logging.getLogger(__name__)

class OHLCVFetcher:
    def __init__(self, bitget_client: BitgetClient, timeframes: dict):
        """
        Initializes the OHLCVFetcher.

        Args:
            bitget_client: An instance of BitgetClient.
            timeframes: A dictionary mapping internal timeframe names to exchange timeframe strings.
                        Example: {'1d': '1d', '4h': '4h', '1h': '1h'}
        """
        self.bitget_client = bitget_client
        self.timeframes = timeframes
        # Determine the number of candles needed for Ichimoku calculation
        # We need enough candles to calculate Ichimoku components, including Senkou Span B
        # and Chikou Span which look back. Standard Ichimoku uses 52 periods for Senkou Span B.
        # We also need a few extra candles to check for 'Fresh' signals and ensure full data.
        # Let's fetch a reasonable number, e.g., 100 candles, which should be sufficient
        # for standard Ichimoku settings and a few lookback candles.
        self.limit_per_timeframe = 100 # Number of candles to fetch for each timeframe

    async def fetch_ohlcv_data_for_symbol(self, symbol: str) -> dict:
        """
        Fetches OHLCV data for a given symbol across all configured timeframes.

        Args:
            symbol: The trading symbol (e.g., 'BTC/USDT').

        Returns:
            A dictionary where keys are internal timeframe names and values are lists of OHLCV data,
            or an empty dictionary if fetching fails for all timeframes.
        """
        all_ohlcv_data = {}
        tasks = []

        for tf_name, tf_exchange in self.timeframes.items():
            # Create a task for each timeframe fetch
            tasks.append(self._fetch_single_timeframe(symbol, tf_name, tf_exchange))

        # Run fetch tasks concurrently
        results = await asyncio.gather(*tasks)

        # Process results
        for tf_name, ohlcv_data in results:
            if ohlcv_data:
                all_ohlcv_data[tf_name] = ohlcv_data
            else:
                logger.warning(f"Failed to fetch OHLCV for {symbol} on timeframe {tf_name}.")

        # Basic check: ensure we got data for at least one timeframe
        if not all_ohlcv_data:
            logger.error(f"Failed to fetch OHLCV data for {symbol} on all configured timeframes.")
            return {}

        return all_ohlcv_data

    async def _fetch_single_timeframe(self, symbol: str, tf_name: str, tf_exchange: str):
        """
        Fetches OHLCV data for a single symbol and timeframe.

        Args:
            symbol: The trading symbol.
            tf_name: The internal timeframe name.
            tf_exchange: The exchange-specific timeframe string.

        Returns:
            A tuple containing the internal timeframe name and the fetched OHLCV data (list),
            or (tf_name, None) if fetching fails.
        """
        logger.info(f"Fetching {self.limit_per_timeframe} candles for {symbol} {tf_name} ({tf_exchange})...")
        ohlcv = await self.bitget_client.fetch_ohlcv(symbol, tf_exchange, limit=self.limit_per_timeframe)

        if ohlcv:
            logger.info(f"Successfully fetched {len(ohlcv)} candles for {symbol} {tf_name}.")
            return (tf_name, ohlcv)
        else:
            logger.warning(f"Failed to fetch OHLCV for {symbol} {tf_name}.")
            return (tf_name, None)

# Example usage (for testing purposes)
# async def main():
#     # This requires a dummy or real BitgetClient instance
#     # For testing, you might mock the BitgetClient or use a real one with paper_trading=True
#     class MockBitgetClient:
#         async def fetch_ohlcv(self, symbol, timeframe, since=None, limit=None, params={}):
#             print(f"Mock fetching {limit} candles for {symbol} {timeframe}")
#             # Return dummy data: [timestamp, open, high, low, close, volume]
#             # Generate some dummy candles
#             dummy_data = []
#             import time
#             current_time_ms = int(time.time() * 1000)
#             # Simple dummy data generation
#             for i in range(limit):
#                 timestamp = current_time_ms - (limit - 1 - i) * 60000 # Simulate 1-minute candles
#                 open_price = 10000 + i * 10
#                 high_price = open_price + 5
#                 low_price = open_price - 5
#                 close_price = open_price + 2
#                 volume = 100 + i * 5
#                 dummy_data.append([timestamp, open_price, high_price, low_price, close_price, volume])
#             return dummy_data

#     # Use the mock client
#     mock_client = MockBitgetClient()
#     timeframes_config = {'1h': '1h', '4h': '4h'} # Example timeframes

#     fetcher = OHLCVFetcher(mock_client, timeframes_config)

#     symbol_to_fetch = 'BTC/USDT'
#     ohlcv_results = await fetcher.fetch_ohlcv_data_for_symbol(symbol_to_fetch)

#     if ohlcv_results:
#         print(f"\nFetched OHLCV data for {symbol_to_fetch}:")
#         for tf, data in ohlcv_results.items():
#             print(f"  {tf}: {len(data)} candles (first: {data[0][0] if data else 'N/A'}, last: {data[-1][0] if data else 'N/A'})")
#     else:
#         print(f"\nFailed to fetch OHLCV data for {symbol_to_fetch}.")

# if __name__ == "__main__":
#     asyncio.run(main())
