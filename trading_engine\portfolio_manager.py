# ichimoku_bitget_bot/trading_engine/portfolio_manager.py

import logging
import time

logger = logging.getLogger(__name__)

class PortfolioManager:
    def __init__(self):
        self.open_positions = {} # Dictionary to store open positions: {symbol: {entry_price, amount, entry_time, order_details}}
        self.trade_history = [] # List to store closed trades

    async def add_open_position(self, symbol, entry_price, amount, order_details):
        """
        Adds a new open position to the portfolio.
        """
        if symbol in self.open_positions:
            logger.warning(f"Position for {symbol} already exists in open positions. This might indicate an issue.")
            # Handle this case: maybe average down, or log an error
            return

        self.open_positions[symbol] = {
            'entry_price': entry_price,
            'amount': amount,
            'entry_time': int(time.time() * 1000), # Entry time in milliseconds
            'order_details': order_details # Store details from the executed order
        }
        logger.info(f"Added open position for {symbol}: {self.open_positions[symbol]}")

    async def close_position(self, symbol, exit_price, order_details):
        """
        Closes an open position and moves it to trade history.
        """
        if symbol not in self.open_positions:
            logger.warning(f"Attempted to close position for {symbol}, but no open position found.")
            return

        position = self.open_positions.pop(symbol)
        exit_time = int(time.time() * 1000)
        profit_loss = (exit_price - position['entry_price']) * position['amount'] # Simple P/L calculation

        trade_record = {
            'symbol': symbol,
            'entry_price': position['entry_price'],
            'entry_time': position['entry_time'],
            'exit_price': exit_price,
            'exit_time': exit_time,
            'amount': position['amount'],
            'profit_loss': profit_loss,
            'entry_order_details': position['order_details'],
            'exit_order_details': order_details # Store details from the exit order
        }
        self.trade_history.append(trade_record)
        logger.info(f"Closed position for {symbol}. P/L: {profit_loss}. Trade record: {trade_record}")

    async def get_open_positions(self):
        """
        Returns a dictionary of current open positions.
        """
        return self.open_positions

    async def get_trade_history(self):
        """
        Returns a list of closed trade records.
        """
        return self.trade_history

    async def get_total_profit_loss(self):
        """
        Calculates the total profit/loss from closed trades.
        """
        total_pl = sum(trade['profit_loss'] for trade in self.trade_history)
        return total_pl

    # Add methods for calculating portfolio value, performance metrics, etc.
