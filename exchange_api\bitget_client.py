# ichimoku_bitget_bot/exchange_api/bitget_client.py

import ccxt
import ccxt.async_support as ccxt_async
import logging
import asyncio

logger = logging.getLogger(__name__)

class BitgetClient:
    def __init__(self, api_key, secret, password=None, paper_trading=False, max_retries=3, retry_delay=5):
        self.api_key = api_key
        self.secret = secret
        self.password = password
        self.paper_trading = paper_trading
        self.exchange = None
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self._initialize_exchange()

    def _initialize_exchange(self):
        """Initializes the CCXT exchange object."""
        try:
            # Use async client for async operations
            self.exchange = ccxt_async.bitget({
                'apiKey': self.api_key,
                'secret': self.secret,
                'password': self.password if self.password is not None else '', # Ensure password is a string
                'options': {
                    'defaultType': 'spot', # Ensure we are trading spot
                    'adjustForTimeDifference': True, # Adjust for potential time sync issues
                    'recvWindow': 10000 # Increase recvWindow for potentially slow connections
                },
                'enableRateLimit': True, # Enable CCXT's built-in rate limiting
            })

            # Set sandbox mode if paper trading is enabled
            if self.paper_trading:
                self.exchange.set_sandbox_mode(True)
                logger.info("Bitget client initialized in PAPER TRADING mode.")
            else:
                logger.info("Bitget client initialized in LIVE TRADING mode.")

        except Exception as e:
            logger.error(f"Error initializing Bitget client: {e}", exc_info=True)
            self.exchange = None # Ensure exchange is None if initialization fails

    async def fetch_balance(self):
        """Fetches the account balance."""
        if not self.exchange:
            logger.error("Exchange not initialized. Cannot fetch balance.")
            return None
        for i in range(self.max_retries):
            try:
                balance = await self.exchange.fetch_balance()
                logger.info(f"Fetched balance: {balance}")
                return balance
            except (ccxt.NetworkError, ccxt.ExchangeNotAvailable, ccxt.RequestTimeout) as e:
                logger.warning(f"Network error fetching balance (attempt {i+1}/{self.max_retries}): {e}. Retrying in {self.retry_delay} seconds...")
                await asyncio.sleep(self.retry_delay)
            except ccxt.BaseError as e:
                logger.error(f"CCXT error fetching balance: {e}", exc_info=True)
                return None
            except Exception as e:
                logger.error(f"An unexpected error occurred while fetching balance: {e}", exc_info=True)
                return None
        logger.error(f"Failed to fetch balance after {self.max_retries} attempts.")
        return None

    async def fetch_ohlcv(self, symbol, timeframe, since=None, limit=None, params={}):
        """Fetches OHLCV data for a symbol and timeframe."""
        if not self.exchange:
            logger.error("Exchange not initialized. Cannot fetch OHLCV.")
            return None
        for i in range(self.max_retries):
            try:
                # Ensure the symbol format is correct for CCXT (e.g., 'BTC/USDT')
                # The bot's settings use 'BTC/USDT', which is CCXT standard
                ohlcv = await self.exchange.fetch_ohlcv(symbol, timeframe, since, limit, params)
                # logger.debug(f"Fetched {len(ohlcv)} OHLCV candles for {symbol} {timeframe}") # Too verbose
                return ohlcv
            except (ccxt.NetworkError, ccxt.ExchangeNotAvailable, ccxt.RequestTimeout) as e:
                logger.warning(f"Network error fetching OHLCV for {symbol} {timeframe} (attempt {i+1}/{self.max_retries}): {e}. Retrying in {self.retry_delay} seconds...")
                await asyncio.sleep(self.retry_delay)
            except ccxt.ExchangeError as e:
                logger.error(f"CCXT Exchange error fetching OHLCV for {symbol} {timeframe}: {e}", exc_info=True)
                return None
            except Exception as e:
                logger.error(f"An unexpected error occurred while fetching OHLCV for {symbol} {timeframe}: {e}", exc_info=True)
                return None
        logger.error(f"Failed to fetch OHLCV for {symbol} {timeframe} after {self.max_retries} attempts.")
        return None

    async def place_order(self, symbol, type, side, amount, price=None, params={}):
        """Places a trading order."""
        if not self.exchange:
            logger.error("Exchange not initialized. Cannot place order.")
            return None
        try:
            # Ensure symbol format is correct
            # Ensure type is 'market' or 'limit'
            # Ensure side is 'buy' or 'sell'
            order = await self.exchange.create_order(symbol, type, side, amount, price, params)
            logger.info(f"Placed {side} {type} order for {amount} of {symbol}. Order ID: {order.get('id')}")
            return order
        except ccxt.InsufficientFunds as e:
            logger.error(f"Insufficient funds to place order for {symbol}: {e}", exc_info=True)
            return None
        except ccxt.InvalidOrder as e:
            logger.error(f"Invalid order parameters for {symbol}: {e}", exc_info=True)
            return None
        except ccxt.DDoSProtection as e:
             logger.error(f"DDoS Protection: Rate limit exceeded on {symbol}: {e}", exc_info=True)
             return None
        except ccxt.ExchangeError as e:
            logger.error(f"CCXT Exchange error placing order for {symbol}: {e}", exc_info=True)
            return None
        except ccxt.NetworkError as e:
            logger.error(f"CCXT Network error placing order for {symbol}: {e}", exc_info=True)
            return None
        except Exception as e:
            logger.error(f"An unexpected error occurred while placing order for {symbol}: {e}", exc_info=True)
            return None

    async def fetch_order_status(self, order_id, symbol=None):
        """Fetches the status of an order."""
        if not self.exchange:
            logger.error("Exchange not initialized. Cannot fetch order status.")
            return None
        try:
            # Some exchanges require symbol to fetch order status
            order = await self.exchange.fetch_order(order_id, symbol)
            logger.info(f"Fetched status for order {order_id}: {order.get('status')}")
            return order
        except ccxt.OrderNotFound as e:
            logger.warning(f"Order {order_id} not found: {e}", exc_info=True)
            return None
        except ccxt.ExchangeError as e:
            logger.error(f"CCXT Exchange error fetching order status {order_id}: {e}", exc_info=True)
            return None
        except ccxt.NetworkError as e:
            logger.error(f"CCXT Network error fetching order status {order_id}: {e}", exc_info=True)
            return None
        except Exception as e:
            logger.error(f"An unexpected error occurred while fetching order status {order_id}: {e}", exc_info=True)
            return None

    async def cancel_order(self, order_id, symbol=None):
        """Cancels an open order."""
        if not self.exchange:
            logger.error("Exchange not initialized. Cannot cancel order.")
            return None
        try:
            # Some exchanges require symbol to cancel order
            cancelled_order = await self.exchange.cancel_order(order_id, symbol)
            logger.info(f"Cancelled order {order_id}.")
            return cancelled_order
        except ccxt.OrderNotFound as e:
            logger.warning(f"Order {order_id} not found, cannot cancel: {e}", exc_info=True)
            return None
        except ccxt.InvalidOrder as e:
             logger.warning(f"Order {order_id} cannot be cancelled (e.g., already filled): {e}", exc_info=True)
             return None
        except ccxt.ExchangeError as e:
            logger.error(f"CCXT Exchange error cancelling order {order_id}: {e}", exc_info=True)
            return None
        except ccxt.NetworkError as e:
            logger.error(f"CCXT Network error cancelling order {order_id}: {e}", exc_info=True)
            return None
        except Exception as e:
            logger.error(f"An unexpected error occurred while cancelling order {order_id}: {e}", exc_info=True)
            return None

    async def close(self):
        """Closes the exchange connection (if necessary)."""
        if self.exchange and hasattr(self.exchange, 'close'):
            await self.exchange.close()
            logger.info("Bitget client connection closed.")

    async def get_market_info(self, symbol):
        """Fetches market information for a given symbol."""
        if not self.exchange:
            logger.error("Exchange not initialized. Cannot fetch market info.")
            return None
        for i in range(self.max_retries):
            try:
                # Ensure markets are loaded
                if not self.exchange.markets:
                    await self.exchange.load_markets()
                
                market = self.exchange.market(symbol)
                logger.debug(f"Fetched market info for {symbol}: {market}")
                return market
            except (ccxt.NetworkError, ccxt.ExchangeNotAvailable, ccxt.RequestTimeout) as e:
                logger.warning(f"Network error fetching market info for {symbol} (attempt {i+1}/{self.max_retries}): {e}. Retrying in {self.retry_delay} seconds...")
                await asyncio.sleep(self.retry_delay)
            except ccxt.ExchangeError as e:
                logger.error(f"CCXT Exchange error fetching market info for {symbol}: {e}", exc_info=True)
                return None
            except Exception as e:
                logger.error(f"An unexpected error occurred while fetching market info for {symbol}: {e}", exc_info=True)
                return None
        logger.error(f"Failed to fetch market info for {symbol} after {self.max_retries} attempts.")
        return None

# Example of how this client might be used (for testing purposes)
# async def main():
#     # Replace with your actual keys or load from settings/env vars
#     api_key = "YOUR_BITGET_API_KEY"
#     secret = "YOUR_BITGET_API_SECRET"
#     password = "YOUR_BITGET_API_PASSWORD" # If required
#     paper_trading = True # Set to True for sandbox

#     client = BitgetClient(api_key, secret, password, paper_trading)

#     # Example: Fetch balance
#     balance = await client.fetch_balance()
#     if balance:
#         print("Balance:", balance)

#     # Example: Fetch OHLCV
#     symbol = 'BTC/USDT'
#     timeframe = '1h'
#     ohlcv_data = await client.fetch_ohlcv(symbol, timeframe, limit=10)
#     if ohlcv_data:
#         print(f"Last 10 {timeframe} candles for {symbol}:", ohlcv_data)

#     # Example: Place a small market buy order (USE WITH CAUTION IN LIVE MODE)
#     # Note: Need to get precise amount based on min order size for the symbol
#     # try:
#     #     # This is a placeholder, actual amount calculation needed
#     #     amount_to_buy = 0.0001 # Example small amount
#     #     order = await client.place_order(symbol, 'market', 'buy', amount_to_buy)
#     #     if order:
#     #         print("Order placed:", order)
#     # except Exception as e:
#     #     print(f"Could not place order: {e}")


#     await client.close()

# if __name__ == "__main__":
#     asyncio.run(main())
